import React, { useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import {
  TextField,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Paper,
  Typography,
  Divider,
  makeStyles
} from "@ellucian/react-design-system/core";
import { useUserInfo } from '@ellucian/experience-extension-utils';
import "./styles.css";

import { library } from '@fortawesome/fontawesome-svg-core';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
/*eslint import/namespace: ['error', { allowComputed: true }]*/
import * as Icons from '@fortawesome/free-solid-svg-icons';

// Add all icons to the library
const iconList = Object
  .keys(Icons)
  .filter(key => key !== "fas" && key !== "prefix")
  .map(icon => Icons[icon]);

library.add(...iconList);

const useStyles = makeStyles((theme) => ({
  root: {
    padding: 0,
    height: '100%',
    display: 'flex',
    flexDirection: 'column'
  },
  searchContainer: {
    display: 'flex',
    alignItems: 'center',
    padding: `${theme.spacing(2)} ${theme.spacing(2)} ${theme.spacing(4)} ${theme.spacing(2)}`,
    gap: theme.spacing(2)
  },
  searchField: {
    flexGrow: 1,
    marginLeft: theme.spacing(3)
  },
  categoryFilter: {
    minWidth: 150,
    maxWidth: '50%',
    flexShrink: 0,
    marginRight: theme.spacing(3)
  },
  listContainer: {
    flexGrow: 1,
    overflow: 'auto'
  },
  list: {
    padding: 0,
  },
  listItem: {
    cursor: 'pointer',
    '&:hover': {
      backgroundColor: '#E9E9E9'
    }
  },
  divider: {
    margin: '0'
  },
  noResults: {
    padding: theme.spacing(2),
    textAlign: 'center'
  },
  icon: {
    minWidth: '40px'
  },
  listItemText: {
    '& .MuiListItemText-primary': {
      fontWeight: 600
    }
  }
}));

function SearchList(props) {
  const classes = useStyles();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [items, setItems] = useState([]);
  const [filteredItems, setFilteredItems] = useState([]);
  const [categories, setCategories] = useState(['all']);
  const [iconColor, setIconColor] = useState('#056CF9'); // Default blue color
  const { roles = [] } = useUserInfo() || {};
  
  // Memoize the function with useCallback to prevent unnecessary recreations
  const isItemVisibleToUser = useCallback((item) => {
    // If no audience is specified, show to everyone
    if (!item.audience || item.audience.trim() === '') {
      return true;
    }
    
    // Get all user roles directly
    const userRoles = roles || [];
    
    // Parse item audience - handle comma-separated values
    const itemAudiences = item.audience
      .split(',')
      .map(audience => audience.trim())
      .filter(audience => audience !== '');
    
    // Check if any of the user's roles match any of the item's audience requirements
    const hasMatchingRole = itemAudiences.some(audience => 
      userRoles.includes(audience)
    );
    
    return hasMatchingRole;
  }, [roles]); // Only recreate when roles change
  
  useEffect(() => {
    const customConfig = props?.cardInfo?.configuration?.customConfiguration;

    let foundItems = [];
    let configuredColor = '#056CF9'; // Default blue color

    if (customConfig?.client?.items) {
      foundItems = customConfig.client.items;
      configuredColor = customConfig.client.iconColor || '#056CF9';
    } else if (customConfig?.customConfiguration?.client?.items) {
      foundItems = customConfig.customConfiguration.client.items;
      configuredColor = customConfig.customConfiguration.client.iconColor || '#056CF9';
    } else if (customConfig?.items) {
      foundItems = customConfig.items;
      configuredColor = customConfig.iconColor || '#056CF9';
    }

    // Set the icon color
    setIconColor(configuredColor);

    if (Array.isArray(foundItems) && foundItems.length > 0) {
      // Filter items based on user's audience
      const visibleItems = foundItems.filter(isItemVisibleToUser);
      setItems(visibleItems);

      // Extract unique categories
      const allCategories = visibleItems
        .map(item => item.category)
        .filter(category => category && category.trim() !== '');

      const uniqueCategories = ['all', ...new Set(allCategories)];
      setCategories(uniqueCategories);
    } else {
      setItems([]);
      setCategories(['all']);
    }
  }, [props, roles, isItemVisibleToUser]);
  
  useEffect(() => {
    // Filter items based on search term and category
    const filtered = items.filter(item => {
      if (!item || (!item.title && !item.description)) return false;
      
      // Category filter
      if (selectedCategory !== 'all' && item.category !== selectedCategory) {
        return false;
      }
      
      // Search term filter
      const searchLower = searchTerm.toLowerCase();
      return (
        (item.title && item.title.toLowerCase().includes(searchLower)) ||
        (item.description && item.description.toLowerCase().includes(searchLower)) ||
        (item.keywords && item.keywords.toLowerCase().includes(searchLower))
      );
    });
    
    setFilteredItems(filtered);
  }, [items, searchTerm, selectedCategory]);
  
  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
  };
  
  const handleCategoryChange = (event) => {
    setSelectedCategory(event.target.value);
  };
  
  const handleItemClick = (url) => {
    if (url) {
      window.open(url, '_blank');
    }
  };
  
  // Simplified render to isolate the issue
  return (
    <Paper className={classes.root}>
      <div className={classes.searchContainer}>
        <TextField
          className={classes.searchField}
          label="Search"
          variant="outlined"
          value={searchTerm}
          onChange={handleSearchChange}
          placeholder="Type to search..."
          size="small"
        />
        
        {categories.length > 1 && (
          <div className={classes.categoryFilter}>
            <select
              value={selectedCategory}
              onChange={handleCategoryChange}
              style={{
                padding: '8px',
                borderRadius: '4px',
                fontSize: '14px',
                border: '1px solid #b2b3b7',
                backgroundColor: 'white',
                width: '100%',
                height: '38px' // Match the height of the TextField
              }}
            >
              {categories.map((category) => (
                <option key={category} value={category}>
                  {category === 'all' ? 'All Categories' : category}
                </option>
              ))}
            </select>
          </div>
        )}
      </div>
      
      <div className={classes.listContainer}>
        {filteredItems.length > 0 ? (
          <List className={classes.list}>
            {filteredItems.map((item, index) => (
              <React.Fragment key={index}>
                <ListItem 
                  className={classes.listItem}
                  onClick={() => handleItemClick(item.url)}
                >
                  {/* Simplified icon rendering */}
                  <ListItemIcon className={classes.icon}>
                    {item.icon ? (
                      <FontAwesomeIcon icon={item.icon} style={{ color: iconColor }} />
                    ) : (
                      <FontAwesomeIcon icon="link" style={{ color: iconColor }} />
                    )}
                  </ListItemIcon>
                  
                  <ListItemText
                    className={classes.listItemText}
                    primary={item.title || ''}
                    secondary={item.description || ''}
                  />
                </ListItem>
                {index < filteredItems.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>
        ) : (
          <Typography className={classes.noResults} variant="body1">
            {searchTerm || selectedCategory !== 'all' ? "No results found" : "No items available"}
          </Typography>
        )}
      </div>
    </Paper>
  );
}

SearchList.propTypes = {
  cardInfo: PropTypes.shape({
    configuration: PropTypes.shape({
      customConfiguration: PropTypes.object
    })
  })
};

export default SearchList;
